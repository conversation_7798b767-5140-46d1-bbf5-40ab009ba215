<?php
/**
 * 实习盖章文件管理类
 * 负责文件上传、下载、存储管理
 * 文件组织结构：uploads/uid/order_id/filename
 */

class SxgzFileManager {

    private $uploadDir;
    private $processedDir;
    private $allowedTypes;
    private $maxFileSize;
    private $maxFilesPerOrder;

    public function __construct() {
        $this->uploadDir = __DIR__ . '/uploads/';
        $this->processedDir = __DIR__ . '/processed/';
        $this->allowedTypes = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/zip',
            'application/x-zip-compressed',
            'application/x-rar-compressed',
            'application/x-7z-compressed',
            'image/jpeg',
            'image/png',
            'image/gif'
        ];
        $this->maxFileSize = 10 * 1024 * 1024; // 10MB
        $this->maxFilesPerOrder = 5; // 每个订单最多5个文件
    }
    
    /**
     * 创建订单专用目录 - 按uid_X/orderid_Y组织
     */
    public function createOrderDirectory($uid, $orderId) {
        $uploadOrderDir = $this->uploadDir . 'uid_' . $uid . '/' . 'orderid_' . $orderId . '/';
        $processedOrderDir = $this->processedDir . 'uid_' . $uid . '/' . 'orderid_' . $orderId . '/';

        if (!is_dir($uploadOrderDir)) {
            mkdir($uploadOrderDir, 0755, true);
        }

        if (!is_dir($processedOrderDir)) {
            mkdir($processedOrderDir, 0755, true);
        }

        return [
            'upload' => $uploadOrderDir,
            'processed' => $processedOrderDir
        ];
    }
    
    /**
     * 验证文件类型和大小
     */
    public function validateFile($file) {
        $errors = [];
        
        // 检查文件大小
        if ($file['size'] > $this->maxFileSize) {
            $errors[] = '文件大小不能超过10MB';
        }
        
        // 检查文件类型
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);
        
        if (!in_array($mimeType, $this->allowedTypes)) {
            $errors[] = '不支持的文件类型';
        }
        
        // 检查文件扩展名
        $allowedExtensions = ['pdf', 'doc', 'docx', 'zip', 'jpg', 'jpeg', 'png', 'gif'];
        $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        
        if (!in_array($fileExtension, $allowedExtensions)) {
            $errors[] = '不支持的文件扩展名';
        }
        
        return $errors;
    }
    
    /**
     * 上传文件 - 支持文件替换检测和多文件管理
     */
    public function uploadFile($file, $uid, $orderId, $isReplacement = false) {
        global $DB;

        $errors = $this->validateFile($file);
        if (!empty($errors)) {
            return ['success' => false, 'errors' => $errors];
        }

        // 检查现有文件数量
        $existingFiles = $this->getOrderFiles($orderId);
        $totalFiles = count($existingFiles['uploads']);

        if (!$isReplacement && $totalFiles >= $this->maxFilesPerOrder) {
            return [
                'success' => false,
                'errors' => ["每个订单最多上传{$this->maxFilesPerOrder}个文件，建议使用压缩包格式"],
                'suggest_compression' => true
            ];
        }

        $directories = $this->createOrderDirectory($uid, $orderId);

        // 生成规范的文件名：uid_orderid_timestamp_originalname
        $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        $originalBaseName = pathinfo($file['name'], PATHINFO_FILENAME);
        $safeBaseName = preg_replace('/[^a-zA-Z0-9\-_\u4e00-\u9fa5]/u', '_', $originalBaseName);
        $safeFileName = $uid . '_' . $orderId . '_' . time() . '_' . $safeBaseName . '.' . $fileExtension;
        $targetPath = $directories['upload'] . $safeFileName;

        // 检查是否存在同名原始文件（替换场景）
        $existingFile = null;
        foreach ($existingFiles['uploads'] as $existingFileInfo) {
            if ($existingFileInfo['original_name'] === $file['name']) {
                $existingFile = $existingFileInfo;
                break;
            }
        }

        if (move_uploaded_file($file['tmp_name'], $targetPath)) {
            $result = [
                'success' => true,
                'filename' => $safeFileName,
                'original_name' => $file['name'],
                'file_path' => $targetPath,
                'file_size' => $file['size'],
                'download_url' => $this->getDownloadUrl($uid, $orderId, $safeFileName, 'upload')
            ];

            if ($existingFile) {
                $result['replaced_file'] = $existingFile['name'];
                $result['is_replacement'] = true;
            }

            return $result;
        } else {
            return ['success' => false, 'errors' => ['文件上传失败']];
        }
    }
    
    /**
     * 获取文件下载URL
     */
    public function getDownloadUrl($uid, $orderId, $filename, $type = 'upload', $fullUrl = false) {
        $baseUrl = '/sxgz/download.php';
        $url = $baseUrl . '?uid=' . $uid . '&order_id=' . $orderId . '&file=' . urlencode($filename) . '&type=' . $type;

        // 如果需要完整URL（用于代理商下载），添加token
        if ($fullUrl) {
            $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
            $host = $_SERVER['HTTP_HOST'] ?? 'localhost';

            // 生成token用于代理商下载验证
            $token = md5($orderId . 'sxgz_download' . date('Y-m-d'));
            $url .= '&token=' . $token;

            $url = $protocol . '://' . $host . $url;
        }

        return $url;
    }

    /**
     * 获取文件预览URL（用于在线预览）
     */
    public function getPreviewUrl($uid, $orderId, $filename, $type = 'upload') {
        $baseUrl = '/sxgz/preview.php';
        return $baseUrl . '?uid=' . $uid . '&order_id=' . $orderId . '&file=' . urlencode($filename) . '&type=' . $type;
    }
    
    /**
     * 删除文件 - 只支持带标识的目录结构
     */
    public function deleteFile($uid, $orderId, $filename, $type = 'upload') {
        $dir = ($type === 'processed') ? $this->processedDir : $this->uploadDir;
        $filePath = $dir . 'uid_' . $uid . '/orderid_' . $orderId . '/' . $filename;

        if (file_exists($filePath)) {
            return unlink($filePath);
        }

        return false;
    }
    
    /**
     * 获取订单文件列表 - 支持预览和下载
     */
    public function getOrderFiles($orderId) {
        global $DB;

        $files = [
            'uploads' => [],
            'processed' => []
        ];

        // 获取订单信息
        $order = $DB->get_row("SELECT uid, uploaded_file, original_filename, file_size, status FROM FD_sxgz_orders WHERE order_id = '{$orderId}'");
        if (!$order) {
            return $files;
        }

        $uid = $order['uid'];

        // 优先检查数据库中的文件记录（用于插件对接的订单）
        if (!empty($order['uploaded_file']) && !empty($order['original_filename'])) {
            $files['uploads'][] = [
                'name' => $order['original_filename'],
                'original_name' => $order['original_filename'],
                'size' => intval($order['file_size']),
                'modified' => time(), // 使用当前时间作为修改时间
                'download_url' => $order['uploaded_file'],
                'preview_url' => null, // 插件文件不支持预览
                'file_type' => $this->getFileTypeFromName($order['original_filename']),
                'can_preview' => false,
                'is_remote' => true // 标记为远程文件
            ];
        } else {
            // 如果数据库中没有文件记录，则扫描本地文件目录
            $uploadDir = $this->uploadDir . 'uid_' . $uid . '/' . 'orderid_' . $orderId . '/';

            if (is_dir($uploadDir)) {
            $uploadFiles = scandir($uploadDir);
            foreach ($uploadFiles as $file) {
                if ($file !== '.' && $file !== '..' && is_file($uploadDir . $file)) {
                    $fileExtension = strtolower(pathinfo($file, PATHINFO_EXTENSION));
                    $files['uploads'][] = [
                        'name' => $file,
                        'original_name' => $this->extractOriginalName($file),
                        'size' => filesize($uploadDir . $file),
                        'modified' => filemtime($uploadDir . $file),
                        'download_url' => $this->getDownloadUrl($uid, $orderId, $file, 'upload'),
                        'preview_url' => $this->canPreview($fileExtension) ? $this->getPreviewUrl($uid, $orderId, $file, 'upload') : null,
                        'file_type' => $this->getFileType($fileExtension),
                        'can_preview' => $this->canPreview($fileExtension)
                    ];
                }
            }
            }
        }

        // 检查处理后的文件目录
        $processedDir = $this->processedDir . 'uid_' . $uid . '/' . 'orderid_' . $orderId . '/';
        if (is_dir($processedDir)) {
            $processedFiles = scandir($processedDir);
            foreach ($processedFiles as $file) {
                if ($file !== '.' && $file !== '..' && is_file($processedDir . $file)) {
                    $fileExtension = strtolower(pathinfo($file, PATHINFO_EXTENSION));
                    $files['processed'][] = [
                        'name' => $file,
                        'original_name' => $this->extractOriginalName($file),
                        'size' => filesize($processedDir . $file),
                        'modified' => filemtime($processedDir . $file),
                        'download_url' => $this->getDownloadUrl($uid, $orderId, $file, 'processed'),
                        'preview_url' => $this->canPreview($fileExtension) ? $this->getPreviewUrl($uid, $orderId, $file, 'processed') : null,
                        'file_type' => $this->getFileType($fileExtension),
                        'can_preview' => $this->canPreview($fileExtension),
                        'is_completed' => in_array($order['status'], ['completed', 'delivered'])
                    ];
                }
            }
        }

        return $files;
    }

    /**
     * 判断文件是否支持在线预览
     */
    private function canPreview($fileExtension) {
        $previewableTypes = ['pdf', 'jpg', 'jpeg', 'png', 'gif'];
        return in_array($fileExtension, $previewableTypes);
    }

    /**
     * 获取文件类型
     */
    private function getFileType($fileExtension) {
        $typeMap = [
            'pdf' => 'PDF文档',
            'doc' => 'Word文档',
            'docx' => 'Word文档',
            'zip' => '压缩包',
            'rar' => '压缩包',
            '7z' => '压缩包',
            'jpg' => '图片',
            'jpeg' => '图片',
            'png' => '图片',
            'gif' => '图片'
        ];

        return $typeMap[$fileExtension] ?? '未知类型';
    }

    /**
     * 根据文件名获取文件类型
     */
    private function getFileTypeFromName($filename) {
        $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
        return $this->getFileType($extension);
    }

    /**
     * 从文件名中提取原始文件名
     * 格式：uid_orderid_timestamp_originalname.ext
     */
    private function extractOriginalName($filename) {
        $parts = explode('_', $filename);
        if (count($parts) >= 4) {
            // 移除前三部分（uid, orderid, timestamp），保留原始文件名
            $originalParts = array_slice($parts, 3);
            return implode('_', $originalParts);
        }
        return $filename; // 如果格式不匹配，返回原文件名
    }

    /**
     * 检查文件数量并给出压缩建议
     */
    public function checkFileCountAndSuggest($orderId) {
        $files = $this->getOrderFiles($orderId);
        $uploadCount = count($files['uploads']);

        if ($uploadCount >= $this->maxFilesPerOrder - 1) {
            return [
                'suggest_compression' => true,
                'message' => "您已上传{$uploadCount}个文件，建议将多个文件打包成压缩包上传，以便管理和传输。",
                'remaining_slots' => $this->maxFilesPerOrder - $uploadCount
            ];
        }

        return [
            'suggest_compression' => false,
            'remaining_slots' => $this->maxFilesPerOrder - $uploadCount
        ];
    }

    /**
     * 获取文件统计信息
     */
    public function getFileStats($orderId) {
        $files = $this->getOrderFiles($orderId);

        $stats = [
            'upload_count' => count($files['uploads']),
            'processed_count' => count($files['processed']),
            'total_upload_size' => 0,
            'total_processed_size' => 0,
            'has_completed_files' => false
        ];

        foreach ($files['uploads'] as $file) {
            $stats['total_upload_size'] += $file['size'];
        }

        foreach ($files['processed'] as $file) {
            $stats['total_processed_size'] += $file['size'];
            if (isset($file['is_completed']) && $file['is_completed']) {
                $stats['has_completed_files'] = true;
            }
        }

        return $stats;
    }
}
?>
