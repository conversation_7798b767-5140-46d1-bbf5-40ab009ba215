<?php
/**
 * 实习盖章源台对接API - 处理代理商请求
 * 
 * 此文件用于接收代理商插件的请求，是源台与代理商之间的桥梁
 * 支持：订单创建、订单查询、订单状态同步
 */

require_once('confing/common.php');

// 获取请求参数
$action = daddslashes($_GET['action'] ?? $_POST['action'] ?? '');
$agent_uid = daddslashes($_GET['uid'] ?? $_POST['uid'] ?? '');
$agent_key = daddslashes($_GET['key'] ?? $_POST['key'] ?? '');
$method = $_SERVER['REQUEST_METHOD'];

// 验证代理商身份
function validateAgent($uid, $key) {
    global $DB;
    
    if (empty($uid) || empty($key)) {
        throw new Exception('缺少认证参数', 401);
    }
    
    // 验证用户存在且key正确
    $user = $DB->get_row("SELECT * FROM qingka_wangke_user WHERE uid = '{$uid}' AND `key` = '{$key}' AND active = 1");
    
    if (!$user) {
        throw new Exception('认证失败', 401);
    }
    
    return $user;
}



// 路由处理
try {
    $agentUser = validateAgent($agent_uid, $agent_key);
    
    switch ($action) {
        case 'create_order':
            if ($method === 'POST') {
                handleCreateOrder($agentUser);
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;
            
        case 'get_order':
            if ($method === 'GET') {
                handleGetOrder($agentUser);
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;
            
        case 'sync_orders':
            if ($method === 'GET') {
                handleSyncOrders($agentUser);
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'update_order_file':
            if ($method === 'POST') {
                handleUpdateOrderFile($agentUser);
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'get_companies_for_agent':
            if ($method === 'GET') {
                handleGetCompanies($agentUser);
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        default:
            throw new Exception('无效的操作', 400);
    }

} catch (Exception $e) {
    http_response_code($e->getCode() ?: 500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'code' => $e->getCode()
    ]);
}

/**
 * 处理代理商订单创建请求
 */
function handleCreateOrder($agentUser) {
    global $DB, $userrow;
    
    // 获取订单数据
    $data = json_decode(file_get_contents('php://input'), true);
    if (!$data) {
        $data = $_POST;
    }
    
    // 验证必要字段
    $required = ['service_type', 'company_id', 'customer_name'];
    foreach ($required as $field) {
        if (empty($data[$field])) {
            throw new Exception("缺少必要字段: {$field}", 400);
        }
    }
    
    // 模拟代理商用户身份创建订单
    $originalUserrow = $userrow ?? null;
    $userrow = $agentUser; // 临时切换用户身份
    
    try {
        // 生成订单号
        $orderNo = 'SXGZ' . date('YmdHis') . rand(1000, 9999);
        
        // 获取实习盖章分类ID
        $categoryRow = $DB->get_row("SELECT id FROM qingka_wangke_fenlei WHERE name = '实习盖章' LIMIT 1");
        if (!$categoryRow) {
            throw new Exception('实习盖章分类不存在', 400);
        }
        
        $company = $DB->get_row("SELECT * FROM qingka_wangke_class WHERE cid = '{$data['company_id']}' AND fenlei = '{$categoryRow['id']}'");
        if (!$company) {
            throw new Exception('公司不存在', 400);
        }
        
        // 计算价格
        $pricing = calculateOrderPricing($data, $company);
        
        // 检查余额
        if ($agentUser['money'] < $pricing['total_price']) {
            throw new Exception('代理商余额不足', 400);
        }
        
        // 准备订单数据
        $insertData = [
            'uid' => $agentUser['uid'],
            'order_no' => $orderNo,
            'service_type' => $data['service_type'],
            'company_id' => $data['company_id'],
            'company_name' => $company['name'],
            'business_license' => intval($data['business_license'] ?? 0),
            'only_business_license' => intval($data['only_business_license'] ?? 0),
            'material_type' => $data['material_type'] ?? 'upload',
            'customer_name' => $data['customer_name'],
            'customer_email' => $data['customer_email'] ?? '',
            'customer_phone' => $data['customer_phone'] ?? '',
            'customer_address' => $data['customer_address'] ?? '',
            'print_copies' => intval($data['print_copies'] ?? 0),
            'special_requirements' => $data['special_requirements'] ?? '',
            'base_price' => $pricing['base_price'],
            'print_price' => $pricing['print_price'],
            'license_price' => $pricing['license_price'],
            'total_price' => $pricing['total_price'],
            'status' => 'pending',
            'created_at' => date('Y-m-d H:i:s'),
            'admin_notes' => '',
            'source' => 'web', // 标记为普通订单
            'agent_uid' => $agentUser['uid'], // 记录代理商UID
            'agent_order_id' => $data['agent_order_id'] ?? null // 记录代理商端的订单ID（如果提供）
        ];
        
        // 插入订单
        $fields = array_keys($insertData);
        $values = array_values($insertData);

        // 转义字符串值
        $escapedValues = array_map(function($value) use ($DB) {
            if (is_string($value)) {
                return "'" . $DB->escape($value) . "'";
            } elseif (is_null($value)) {
                return 'NULL';
            } else {
                return $value;
            }
        }, $values);

        $sql = "INSERT INTO FD_sxgz_orders (`" . implode('`, `', $fields) . "`) VALUES (" . implode(', ', $escapedValues) . ")";

        $result = $DB->query($sql);
        if (!$result) {
            throw new Exception('订单创建失败: ' . $DB->error(), 500);
        }

        // 获取插入的订单ID - 通过查询获取
        $orderRecord = $DB->get_row("SELECT order_id FROM FD_sxgz_orders WHERE order_no = '{$orderNo}' LIMIT 1");
        $orderId = $orderRecord ? intval($orderRecord['order_id']) : 0;

        if (!$orderId) {
            throw new Exception('订单创建成功但无法获取订单ID', 500);
        }
        
        // 扣除代理商余额
        $DB->query("UPDATE qingka_wangke_user SET money = money - {$pricing['total_price']} WHERE uid = {$agentUser['uid']}");
        
        // 记录日志
        wlog($agentUser['uid'], 'sxgzapi下单', "订单号: {$orderNo}, 金额: {$pricing['total_price']}元", -$pricing['total_price']);
        
        // 恢复原用户身份
        $userrow = $originalUserrow;
        
        echo json_encode([
            'success' => true,
            'message' => '订单创建成功',
            'data' => [
                'order_id' => $orderId,
                'order_no' => $orderNo,
                'total_price' => $pricing['total_price']
            ]
        ]);
        
    } catch (Exception $e) {
        // 恢复原用户身份
        $userrow = $originalUserrow;
        throw $e;
    }
}

/**
 * 处理订单查询
 */
function handleGetOrder($agentUser) {
    global $DB;
    
    $orderId = daddslashes($_GET['order_id'] ?? '');
    if (empty($orderId)) {
        throw new Exception('缺少订单ID', 400);
    }
    
    // 查询订单（只能查询自己的订单）
    $order = $DB->get_row("SELECT * FROM FD_sxgz_orders WHERE order_id = '{$orderId}' AND uid = '{$agentUser['uid']}'");
    
    if (!$order) {
        throw new Exception('订单不存在', 404);
    }
    
    echo json_encode([
        'success' => true,
        'data' => $order
    ]);
}

/**
 * 处理批量订单同步
 */
function handleSyncOrders($agentUser) {
    global $DB;

    $page = intval($_GET['page'] ?? 1);
    $limit = intval($_GET['limit'] ?? 50);
    $offset = ($page - 1) * $limit;

    // 获取代理商的订单，包含处理后文件URL
    $orders = $DB->get_results(
        "SELECT order_id, order_no, status, admin_notes, completed_at, updated_at,
                tracking_number, courier_company, processed_files,
                COALESCE(processed_file_url, '') as processed_file_url,
                original_filename, file_size
         FROM FD_sxgz_orders 
         WHERE uid = '{$agentUser['uid']}'
         ORDER BY created_at DESC
         LIMIT {$offset}, {$limit}"
    );

    $total = $DB->count("SELECT COUNT(*) FROM FD_sxgz_orders WHERE uid = '{$agentUser['uid']}'");

    echo json_encode([
        'success' => true,
        'data' => $orders,
        'pagination' => [
            'current_page' => $page,
            'total' => $total,
            'per_page' => $limit
        ]
    ]);
}

/**
 * 处理代理商文件URL更新请求
 */
function handleUpdateOrderFile($agentUser) {
    global $DB;

    // 获取文件数据
    $data = json_decode(file_get_contents('php://input'), true);
    if (!$data) {
        $data = $_POST;
    }

    // 验证必要字段 - 支持 order_id 或 order_no
    if (empty($data['file_url'])) {
        throw new Exception("缺少必要字段: file_url", 400);
    }

    if (empty($data['order_id']) && empty($data['order_no'])) {
        throw new Exception("缺少必要字段: order_id 或 order_no", 400);
    }

    $fileUrl = $DB->escape($data['file_url']);
    $originalFilename = $DB->escape($data['original_filename'] ?? '');
    $fileSize = intval($data['file_size'] ?? 0);
    $pluginDomain = $DB->escape($data['plugin_domain'] ?? '');

    // 根据提供的字段查找订单
    $order = null;
    if (!empty($data['order_id'])) {
        // 使用订单ID查找
        $orderId = intval($data['order_id']);
        $order = $DB->get_row("SELECT * FROM FD_sxgz_orders WHERE order_id = '{$orderId}' AND uid = '{$agentUser['uid']}'");
    } elseif (!empty($data['order_no'])) {
        // 使用订单号查找
        $orderNo = $DB->escape($data['order_no']);
        $order = $DB->get_row("SELECT * FROM FD_sxgz_orders WHERE order_no = '{$orderNo}' AND uid = '{$agentUser['uid']}'");
        if ($order) {
            $orderId = intval($order['order_id']);
        }
    }

    if (!$order) {
        throw new Exception('订单不存在或无权限访问', 404);
    }

    // 更新订单的文件信息
    $updateSql = "UPDATE FD_sxgz_orders SET
                    uploaded_file = '{$fileUrl}',
                    original_filename = '{$originalFilename}',
                    file_size = {$fileSize},
                    updated_at = NOW()
                  WHERE order_id = '{$orderId}'";

    $result = $DB->query($updateSql);

    if ($result) {
        // 记录文件URL更新日志
        $logMessage = "文件URL已更新: " . ($data['original_filename'] ?? '未知文件');

        echo json_encode([
            'success' => true,
            'message' => '文件URL更新成功',
            'data' => [
                'order_id' => $orderId,
                'order_no' => $order['order_no'],
                'file_url' => $data['file_url'],
                'original_filename' => $data['original_filename'],
                'file_size' => $fileSize,
                'plugin_domain' => $pluginDomain,
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ]);
    } else {
        throw new Exception('文件URL更新失败', 500);
    }
}

/**
 * 计算订单价格
 */
function calculateOrderPricing($data, $company) {
    $basePrice = floatval($company['price']);
    $printPrice = 0;
    $licensePrice = 0;
    
    // 计算打印费用
    if (!empty($data['print_copies'])) {
        $printPrice = intval($data['print_copies']) * 2; // 假设每章2元
    }
    
    // 计算营业执照费用
    if (!empty($data['business_license'])) {
        $licensePrice = 50; // 假设营业执照50元
    }
    
    $totalPrice = $basePrice + $printPrice + $licensePrice;
    
    return [
        'base_price' => $basePrice,
        'print_price' => $printPrice,
        'license_price' => $licensePrice,
        'total_price' => $totalPrice
    ];
}

/**
 * 处理代理商获取公司列表请求
 */
function handleGetCompanies($agentUser) {
    global $DB;

    try {
        // 获取实习盖章分类ID
        $categoryRow = $DB->get_row("SELECT id FROM qingka_wangke_fenlei WHERE name = '实习盖章' LIMIT 1");
        if (!$categoryRow) {
            throw new Exception('实习盖章分类不存在', 500);
        }

        // 获取该分类下的所有公司
        $companies = $DB->get_results("
            SELECT cid, name, price, content, status, fenlei, addtime
            FROM qingka_wangke_class
            WHERE fenlei = '{$categoryRow['id']}'
            AND status = 1
            ORDER BY addtime DESC
        ");

        if (!$companies) {
            $companies = [];
        }

        // 记录操作日志
        error_log("代理商获取公司列表 - UID: {$agentUser['uid']}, 公司数量: " . count($companies));

        echo json_encode([
            'success' => true,
            'data' => $companies,
            'message' => '公司列表获取成功',
            'count' => count($companies)
        ]);

    } catch (Exception $e) {
        error_log("获取公司列表失败 - UID: {$agentUser['uid']}, 错误: " . $e->getMessage());
        throw $e;
    }
}
?>
